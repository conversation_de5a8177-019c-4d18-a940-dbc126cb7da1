# PowerShell script to set up frontend and backend repositories

Write-Host "Setting up I-Invoyisi repositories for deployment..." -ForegroundColor Green

# Create frontend repository
Write-Host "Creating frontend repository..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "..\invoyisi-frontend"
Set-Location "..\invoyisi-frontend"
git init

# Copy frontend files
Write-Host "Copying frontend files..." -ForegroundColor Yellow
Copy-Item -Path "..\I-Invoyisi-main\src" -Destination ".\src" -Recurse -Force
Copy-Item -Path "..\I-Invoyisi-main\public" -Destination ".\public" -Recurse -Force
Copy-Item -Path "..\I-Invoyisi-main\package.json" -Destination ".\" -Force
Copy-Item -Path "..\I-Invoyisi-main\package-lock.json" -Destination ".\" -Force
Copy-Item -Path "..\I-Invoyisi-main\vite.config.ts" -Destination ".\" -Force
Copy-Item -Path "..\I-Invoyisi-main\tsconfig*.json" -Destination ".\" -Force
Copy-Item -Path "..\I-Invoyisi-main\tailwind.config.js" -Destination ".\" -Force
Copy-Item -Path "..\I-Invoyisi-main\postcss.config.js" -Destination ".\" -Force
Copy-Item -Path "..\I-Invoyisi-main\eslint.config.js" -Destination ".\" -Force
Copy-Item -Path "..\I-Invoyisi-main\index.html" -Destination ".\" -Force
Copy-Item -Path "..\I-Invoyisi-main\.env.example" -Destination ".\" -Force
Copy-Item -Path "..\I-Invoyisi-main\README-frontend.md" -Destination ".\README.md" -Force
Copy-Item -Path "..\I-Invoyisi-main\render.yaml" -Destination ".\" -Force

# Create frontend .gitignore
@"
node_modules/
dist/
.env
.env.local
.env.production.local
.DS_Store
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.vscode/
.idea/
"@ | Out-File -FilePath ".gitignore" -Encoding UTF8

# Setup frontend git
git add .
git commit -m "Initial frontend setup for deployment"
git remote add origin https://github.com/Tcaline10/invoyisi-frontend.git
git branch -M main

Write-Host "Frontend repository ready. Run 'git push -u origin main' to push." -ForegroundColor Green

# Create backend repository
Write-Host "Creating backend repository..." -ForegroundColor Yellow
Set-Location ".."
New-Item -ItemType Directory -Force -Path "invoyisi-backend"
Set-Location "invoyisi-backend"
git init

# Copy backend files
Write-Host "Copying backend files..." -ForegroundColor Yellow
Copy-Item -Path "..\I-Invoyisi-main\backend\*" -Destination ".\" -Recurse -Force

# Create backend .gitignore
@"
__pycache__/
*.pyc
.env
.venv/
venv/
.pytest_cache/
.coverage
htmlcov/
dist/
build/
*.egg-info/
.DS_Store
*.log
"@ | Out-File -FilePath ".gitignore" -Encoding UTF8

# Create backend README
$readmeContent = @"
# I-Invoyisi Backend

FastAPI backend for the I-Invoyisi invoice management system.

## Deployment on Render

### Settings:
- **Build Command**: pip install -r requirements.txt
- **Start Command**: uvicorn app.main:app --host 0.0.0.0 --port `$PORT
- **Environment**: Python 3.11

### Environment Variables:
- SUPABASE_URL
- SUPABASE_KEY
- DATABASE_URL
- SECRET_KEY
- DEBUG=false

## Local Development

1. Install dependencies:
pip install -r requirements.txt

2. Set up environment variables in .env

3. Run the application:
uvicorn app.main:app --reload
"@

$readmeContent | Out-File -FilePath "README.md" -Encoding UTF8

# Setup backend git
git add .
git commit -m "Initial backend setup for deployment"
git remote add origin https://github.com/Tcaline10/invoyisi-backend.git
git branch -M main

Write-Host "Backend repository ready. Run 'git push -u origin main' to push." -ForegroundColor Green

Write-Host "Repository setup complete!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. cd ..\invoyisi-frontend && git push -u origin main" -ForegroundColor White
Write-Host "2. cd ..\invoyisi-backend && git push -u origin main" -ForegroundColor White
Write-Host "3. Set up deployments on Render.com using the settings in DEPLOYMENT.md" -ForegroundColor White
