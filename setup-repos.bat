@echo off
echo Setting up I-Invoyisi repositories for deployment...

echo Creating frontend repository...
mkdir ..\invoyisi-frontend
cd ..\invoyisi-frontend
git init

echo Copying frontend files...
xcopy /E /I /Y ..\I-Invoyisi-main\src src
xcopy /E /I /Y ..\I-Invoyisi-main\public public
copy ..\I-Invoyisi-main\package.json .
copy ..\I-Invoyisi-main\package-lock.json .
copy ..\I-Invoyisi-main\vite.config.ts .
copy ..\I-Invoyisi-main\tsconfig*.json .
copy ..\I-Invoyisi-main\tailwind.config.js .
copy ..\I-Invoyisi-main\postcss.config.js .
copy ..\I-Invoyisi-main\eslint.config.js .
copy ..\I-Invoyisi-main\index.html .
copy ..\I-Invoyisi-main\.env.example .
copy ..\I-Invoyisi-main\README-frontend.md README.md
copy ..\I-Invoyisi-main\render.yaml .

echo node_modules/ > .gitignore
echo dist/ >> .gitignore
echo .env >> .gitignore
echo .env.local >> .gitignore
echo .env.production.local >> .gitignore
echo .DS_Store >> .gitignore
echo *.log >> .gitignore

git add .
git commit -m "Initial frontend setup for deployment"
git remote add origin https://github.com/Tcaline10/invoyisi-frontend.git
git branch -M main

echo Frontend repository ready!

echo Creating backend repository...
cd ..
mkdir invoyisi-backend
cd invoyisi-backend
git init

echo Copying backend files...
xcopy /E /I /Y ..\I-Invoyisi-main\backend\* .

echo __pycache__/ > .gitignore
echo *.pyc >> .gitignore
echo .env >> .gitignore
echo .venv/ >> .gitignore
echo venv/ >> .gitignore
echo .pytest_cache/ >> .gitignore
echo .coverage >> .gitignore
echo htmlcov/ >> .gitignore
echo dist/ >> .gitignore
echo build/ >> .gitignore
echo *.egg-info/ >> .gitignore

echo # I-Invoyisi Backend > README.md
echo. >> README.md
echo FastAPI backend for the I-Invoyisi invoice management system. >> README.md

git add .
git commit -m "Initial backend setup for deployment"
git remote add origin https://github.com/Tcaline10/invoyisi-backend.git
git branch -M main

echo Backend repository ready!
echo.
echo Repository setup complete!
echo Next steps:
echo 1. cd ..\invoyisi-frontend ^&^& git push -u origin main
echo 2. cd ..\invoyisi-backend ^&^& git push -u origin main
echo 3. Set up deployments on Render.com

pause
