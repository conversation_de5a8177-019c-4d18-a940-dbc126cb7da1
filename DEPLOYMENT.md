# Deployment Guide for I-Invoyisi

## Frontend Deployment on Render

### Repository Setup

1. **Create Frontend Repository**
   ```bash
   # Create a new directory for frontend
   mkdir invoyisi-frontend
   cd invoyisi-frontend
   git init
   
   # Copy frontend files
   cp -r ../I-Invoyisi-main/src ./
   cp ../I-Invoyisi-main/package.json ./
   cp ../I-Invoyisi-main/package-lock.json ./
   cp ../I-Invoyisi-main/vite.config.ts ./
   cp ../I-Invoyisi-main/tsconfig*.json ./
   cp ../I-Invoyisi-main/tailwind.config.js ./
   cp ../I-Invoyisi-main/postcss.config.js ./
   cp ../I-Invoyisi-main/eslint.config.js ./
   cp ../I-Invoyisi-main/index.html ./
   cp -r ../I-Invoyisi-main/public ./
   cp ../I-Invoyisi-main/.env.example ./
   cp ../I-Invoyisi-main/README-frontend.md ./README.md
   cp ../I-Invoyisi-main/render.yaml ./
   
   # Create .gitignore
   echo "node_modules/
   dist/
   .env
   .env.local
   .env.production.local
   .DS_Store
   *.log" > .gitignore
   
   # Add and commit
   git add .
   git commit -m "Initial frontend setup for deployment"
   
   # Add remote and push
   git remote add origin https://github.com/Tcaline10/invoyisi-frontend.git
   git branch -M main
   git push -u origin main
   ```

2. **Backend Repository Setup**
   ```bash
   # Create a new directory for backend
   mkdir invoyisi-backend
   cd invoyisi-backend
   git init
   
   # Copy backend files
   cp -r ../I-Invoyisi-main/backend/* ./
   
   # Create .gitignore for backend
   echo "__pycache__/
   *.pyc
   .env
   .venv/
   venv/
   .pytest_cache/
   .coverage
   htmlcov/
   dist/
   build/
   *.egg-info/" > .gitignore
   
   # Create README
   echo "# I-Invoyisi Backend
   
   FastAPI backend for the I-Invoyisi invoice management system.
   
   ## Deployment on Render
   
   ### Settings:
   - **Build Command**: \`pip install -r requirements.txt\`
   - **Start Command**: \`uvicorn app.main:app --host 0.0.0.0 --port \$PORT\`
   - **Environment**: Python 3.11
   
   ### Environment Variables:
   - SUPABASE_URL
   - SUPABASE_KEY
   - DATABASE_URL
   - SECRET_KEY
   - DEBUG=false
   " > README.md
   
   # Add and commit
   git add .
   git commit -m "Initial backend setup for deployment"
   
   # Add remote and push
   git remote add origin https://github.com/Tcaline10/invoyisi-backend.git
   git branch -M main
   git push -u origin main
   ```

## Render Deployment Settings

### Frontend (Static Site)
- **Repository**: https://github.com/Tcaline10/invoyisi-frontend.git
- **Branch**: main
- **Build Command**: `npm install && npm run build`
- **Publish Directory**: `dist`
- **Environment Variables**:
  - `VITE_SUPABASE_URL`: Your Supabase project URL
  - `VITE_SUPABASE_ANON_KEY`: Your Supabase anonymous key

### Backend (Web Service)
- **Repository**: https://github.com/Tcaline10/invoyisi-backend.git
- **Branch**: main
- **Build Command**: `pip install -r requirements.txt`
- **Start Command**: `uvicorn app.main:app --host 0.0.0.0 --port $PORT`
- **Environment**: Python 3.11
- **Environment Variables**:
  - `SUPABASE_URL`: Your Supabase project URL
  - `SUPABASE_KEY`: Your Supabase service role key
  - `DATABASE_URL`: Your database connection string
  - `SECRET_KEY`: A secure random string
  - `DEBUG`: false

## Post-Deployment Steps

1. **Update CORS settings** in your backend to allow your frontend domain
2. **Set up custom domains** if needed
3. **Configure environment variables** in Render dashboard
4. **Test the deployment** thoroughly

## Troubleshooting

### Frontend Issues
- Ensure `dist` folder is being generated during build
- Check that environment variables are properly set
- Verify routing works with the `_redirects` file

### Backend Issues
- Check that all dependencies are in `requirements.txt`
- Ensure the start command is correct
- Verify database connections and environment variables
